import { NavLink } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Pill,
  Ambulance,
  Handshake,
  FileText,
  Settings,
  LogOut,
  Wrench,
  MessageSquare,
  Share2,
  Image,
  Contact,
  Menu,
  X,
  Library,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import supabase from "@/utils/supabase";
import { useEffect, useState } from "react";
import { infoMeddocService } from "@/services/infoMeddocService";
import { Skeleton } from "@/components/ui/skeleton";
import { NotificationCount } from "@/components/dashboard/NotificationCount";
import { useNotifications } from "@/hooks/useNotifications";

interface SidebarProps {
  className?: string;
}

const Sidebar = ({ className }: SidebarProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [info, setInfo] = useState<Info_page_meddoc | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const { unreadContacts, unreadMessages, totalUnread } = useNotifications();

  useEffect(() => {
    const fetchInfo = async () => {
      try {
        setIsLoading(true);
        const data = await infoMeddocService.getInfo();
        setInfo(data);
      } catch (error) {
        console.error("Error fetching site info:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInfo();
  }, []);

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      toast({
        title: "Déconnexion réussie",
        description: "À bientôt !",
      });
      navigate("/login");
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: error.message,
      });
    }
  };

  const menuItems = [
    {
      title: "Tableau de bord",
      icon: LayoutDashboard,
      href: "/dashboard",
    },
    {
      title: "Gestion MEDDoC",
      items: [
        {
          title: "Page MEDDoC",
          icon: FileText,
          href: "/dashboard/page-meddoc",
        },
        {
          title: "Partenaires",
          icon: Handshake,
          href: "/dashboard/partenaires/list",
        },
        {
          title: "Services",
          icon: Wrench,
          href: "/dashboard/services",
        },
        {
          title: "Nos Contacts",
          icon: Contact,
          href: "/dashboard/contact-meddoc",
        },
        {
          title: "Réseaux sociaux",
          icon: Share2,
          href: "/dashboard/reseaux-sociaux",
        },
      ],
    },
    {
      title: "Gestion Santé",
      items: [
        {
          title: "Pharmacies",
          icon: Pill,
          href: "/dashboard/pharmacies",
        },
        {
          title: "Ambulances",
          icon: Ambulance,
          href: "/dashboard/ambulances",
        },
        {
          title: "Bibliothèque",
          icon: Library,
          href: "/dashboard/bibliotheque",
        },
      ],
    },
  ];

  return (
    <div>
      {/* Barre latérale */}
      <div
        className={cn(
          "fixed top-0 left-0 z-40 h-full bg-white shadow-lg transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full",
          "md:translate-x-0 md:relative md:block",
        )}
      >
        <div className={cn("pb-12 min-h-screen", className)}>
          {/* Contenu de la barre latérale */}
          <div className="space-y-4 py-4">
            <div className="px-3 py-2">
              <div className="mb-4 flex justify-center items-center gap-2">
                {isLoading ? (
                  <Skeleton className="h-8 w-32 mx-auto" />
                ) : info?.logo ? (
                  <img
                    src={info.logo}
                    alt="MEDDoC Logo"
                    className="h-12 object-contain"
                  />
                ) : null}
                {/* <h2 className="text-2xl font-bold text-center text-meddoc-primary">
                  MEDDoC
                </h2> */}
              </div>
              {/* Liste des éléments du menu */}
              <ScrollArea className="h-[calc(100vh-12rem)]">
                {menuItems.map((item, index) => (
                  <div key={index} className="mb-4">
                    {item.items ? (
                      <>
                        <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight text-primary">
                          {item.title}
                        </h2>
                        <div className="space-y-1">
                          {item.items.map((subItem, subIndex) => (
                            <NavLink
                              key={subIndex}
                              to={subItem.href}
                              className={({ isActive }) =>
                                cn(
                                  "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-primary",
                                  isActive
                                    ? "bg-primary/10 text-primary"
                                    : "text-muted-foreground hover:bg-primary/5",
                                )
                              }
                            >
                              <subItem.icon className="h-4 w-4" />
                              {subItem.title}
                              {subItem.href.includes("/contact-meddoc") && (
                                <NotificationCount count={unreadContacts} />
                              )}
                              {subItem.href.includes("/messages") && (
                                <NotificationCount count={unreadMessages} />
                              )}
                            </NavLink>
                          ))}
                        </div>
                      </>
                    ) : (
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-primary",
                            isActive
                              ? "bg-primary/10 text-primary"
                              : "text-muted-foreground hover:bg-primary/5",
                          )
                        }
                      >
                        <item.icon className="h-4 w-4" />
                        {item.title}
                      </NavLink>
                    )}
                  </div>
                ))}
              </ScrollArea>
            </div>
          </div>
          {/* Bouton déconnexion */}
          <div className="px-3 absolute bottom-4 w-full">
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 text-muted-foreground hover:text-destructive"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              Déconnexion
            </Button>
          </div>
        </div>
        {/* Bouton pour afficher/masquer la barre latérale */}
        <button
          className="m fixed top-0 right-0 z-50 p-2 bg-primary text-white rounded-md md:hidden"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
